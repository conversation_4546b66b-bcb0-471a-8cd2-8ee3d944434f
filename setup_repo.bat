@echo off
echo 🚀 LotteryAnalysis GitHub Repository Setup
echo ==========================================

echo.
echo Step 1: Checking if Git is installed...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed or not in PATH
    echo Please install Git from https://git-scm.com/
    echo Then run this script again.
    pause
    exit /b 1
)
echo ✅ Git is installed

echo.
echo Step 2: Current directory contents:
dir /b

echo.
echo Step 3: Instructions for GitHub setup:
echo.
echo 1. Go to https://github.com/starsoft007
echo 2. Click "New repository"
echo 3. Name it "LotteryAnalysis"
echo 4. Make it public
echo 5. Initialize with README
echo 6. Add Python .gitignore
echo 7. Add MIT license
echo.

set /p continue="Have you created the repository on GitHub? (y/n): "
if /i "%continue%" neq "y" (
    echo Please create the repository first, then run this script again.
    pause
    exit /b 0
)

echo.
echo Step 4: Setting up local repository...

REM Initialize git if not already done
if not exist ".git" (
    echo Initializing Git repository...
    git init
    git branch -M main
)

echo Adding files to Git...
git add .

echo Committing files...
git commit -m "Initial commit: Complete Loto 7 prediction system

- Add Monte Carlo simulation engine  
- Add web scraper for historical data
- Add prediction report generator
- Add Excel export functionality
- Include 630 historical draws dataset
- Add comprehensive documentation"

echo.
echo Step 5: Adding remote origin...
git remote remove origin >nul 2>&1
git remote add origin https://github.com/starsoft007/LotteryAnalysis.git

echo.
echo Step 6: Pushing to GitHub...
git push -u origin main

if %errorlevel% equ 0 (
    echo.
    echo ✅ Successfully uploaded to GitHub!
    echo 🌐 Repository URL: https://github.com/starsoft007/LotteryAnalysis
    echo.
    echo Next steps:
    echo - Visit your repository to verify upload
    echo - Add topics/tags in repository settings
    echo - Consider creating a release
) else (
    echo.
    echo ❌ Push failed. This might be due to:
    echo - Authentication issues
    echo - Repository doesn't exist
    echo - Network problems
    echo.
    echo Please check the error message above and try again.
)

echo.
pause
