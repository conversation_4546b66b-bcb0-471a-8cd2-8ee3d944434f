#!/usr/bin/env python3
"""
Loto 7 蒙特卡洛预测器
基于历史数据使用蒙特卡洛方法预测下次中奖号码
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import random
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class Loto7MonteCarloPredictor:
    def __init__(self, csv_file='loto7_all_results.csv'):
        """初始化预测器"""
        self.df = pd.read_csv(csv_file)
        self.main_numbers = []
        self.bonus_numbers = []
        self.load_data()
        
    def load_data(self):
        """加载和处理数据"""
        print("正在加载历史数据...")
        
        # 提取所有主号码
        for i in range(1, 8):
            self.main_numbers.extend(self.df[f'number_{i}'].tolist())
        
        # 提取所有奖励号码
        self.bonus_numbers.extend(self.df['bonus_1'].tolist())
        self.bonus_numbers.extend(self.df['bonus_2'].tolist())
        
        print(f"已加载 {len(self.df)} 期历史数据")
        print(f"主号码总数: {len(self.main_numbers)}")
        print(f"奖励号码总数: {len(self.bonus_numbers)}")
    
    def analyze_frequency(self):
        """分析号码频率"""
        print("\n=== 号码频率分析 ===")
        
        # 主号码频率
        main_freq = Counter(self.main_numbers)
        bonus_freq = Counter(self.bonus_numbers)
        
        print("主号码出现频率 (前10名):")
        for num, freq in main_freq.most_common(10):
            print(f"  {num:2d}: {freq:3d}次 ({freq/len(self.main_numbers)*100:.1f}%)")
        
        print("\n奖励号码出现频率 (前10名):")
        for num, freq in bonus_freq.most_common(10):
            print(f"  {num:2d}: {freq:3d}次 ({freq/len(self.bonus_numbers)*100:.1f}%)")
        
        return main_freq, bonus_freq
    
    def calculate_probabilities(self):
        """计算各号码的概率分布"""
        main_freq = Counter(self.main_numbers)
        bonus_freq = Counter(self.bonus_numbers)
        
        # 计算主号码概率 (1-37)
        main_probs = {}
        total_main = len(self.main_numbers)
        for i in range(1, 38):
            main_probs[i] = main_freq.get(i, 0) / total_main
        
        # 计算奖励号码概率 (1-37)
        bonus_probs = {}
        total_bonus = len(self.bonus_numbers)
        for i in range(1, 38):
            bonus_probs[i] = bonus_freq.get(i, 0) / total_bonus
        
        return main_probs, bonus_probs
    
    def analyze_patterns(self):
        """分析号码模式"""
        print("\n=== 号码模式分析 ===")
        
        # 分析连续号码出现情况
        consecutive_counts = []
        for _, row in self.df.iterrows():
            numbers = sorted([row[f'number_{i}'] for i in range(1, 8)])
            consecutive = 0
            for i in range(len(numbers)-1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive += 1
            consecutive_counts.append(consecutive)
        
        print(f"平均连续号码对数: {np.mean(consecutive_counts):.2f}")
        
        # 分析奇偶分布
        odd_even_dist = []
        for _, row in self.df.iterrows():
            numbers = [row[f'number_{i}'] for i in range(1, 8)]
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            odd_even_dist.append(odd_count)
        
        print(f"平均奇数个数: {np.mean(odd_even_dist):.2f}")
        
        # 分析数字和
        number_sums = []
        for _, row in self.df.iterrows():
            numbers = [row[f'number_{i}'] for i in range(1, 8)]
            number_sums.append(sum(numbers))
        
        print(f"号码和范围: {min(number_sums)} - {max(number_sums)}")
        print(f"平均号码和: {np.mean(number_sums):.1f}")
        
        return consecutive_counts, odd_even_dist, number_sums
    
    def monte_carlo_simulation(self, num_simulations=100000):
        """蒙特卡洛模拟预测"""
        print(f"\n=== 蒙特卡洛模拟 ({num_simulations:,}次) ===")
        
        main_probs, bonus_probs = self.calculate_probabilities()
        
        # 创建加权选择列表
        main_numbers_weighted = []
        bonus_numbers_weighted = []
        
        for num in range(1, 38):
            # 主号码权重 (基于历史频率 + 随机因子)
            weight = int(main_probs[num] * 10000) + random.randint(1, 100)
            main_numbers_weighted.extend([num] * weight)
            
            # 奖励号码权重
            weight = int(bonus_probs[num] * 10000) + random.randint(1, 100)
            bonus_numbers_weighted.extend([num] * weight)
        
        # 进行蒙特卡洛模拟
        simulated_combinations = []
        
        for _ in range(num_simulations):
            # 生成7个主号码 (不重复)
            main_combo = []
            attempts = 0
            while len(main_combo) < 7 and attempts < 100:
                num = random.choice(main_numbers_weighted)
                if num not in main_combo:
                    main_combo.append(num)
                attempts += 1
            
            if len(main_combo) == 7:
                main_combo.sort()
                
                # 生成2个奖励号码 (不与主号码重复)
                bonus_combo = []
                attempts = 0
                while len(bonus_combo) < 2 and attempts < 100:
                    num = random.choice(bonus_numbers_weighted)
                    if num not in main_combo and num not in bonus_combo:
                        bonus_combo.append(num)
                    attempts += 1
                
                if len(bonus_combo) == 2:
                    bonus_combo.sort()
                    simulated_combinations.append((tuple(main_combo), tuple(bonus_combo)))
        
        print(f"成功生成 {len(simulated_combinations):,} 个有效组合")
        return simulated_combinations
    
    def select_best_predictions(self, simulated_combinations, top_n=5):
        """选择最佳预测组合"""
        print(f"\n=== 选择最佳预测 (前{top_n}名) ===")
        
        # 统计组合出现频率
        combo_counter = Counter(simulated_combinations)
        
        # 获取最频繁的组合
        top_combinations = combo_counter.most_common(top_n)
        
        predictions = []
        for i, (combo, freq) in enumerate(top_combinations, 1):
            main_nums, bonus_nums = combo
            confidence = freq / len(simulated_combinations) * 100
            
            print(f"\n预测 #{i} (置信度: {confidence:.4f}%):")
            print(f"  主号码: {' '.join(f'{n:2d}' for n in main_nums)}")
            print(f"  奖励号码: {' '.join(f'{n:2d}' for n in bonus_nums)}")
            print(f"  出现次数: {freq:,}")
            
            predictions.append({
                'rank': i,
                'main_numbers': list(main_nums),
                'bonus_numbers': list(bonus_nums),
                'frequency': freq,
                'confidence': confidence
            })
        
        return predictions
    
    def generate_additional_predictions(self, num_predictions=10):
        """生成额外的预测组合"""
        print(f"\n=== 生成额外预测 ({num_predictions}组) ===")
        
        main_probs, bonus_probs = self.calculate_probabilities()
        
        # 基于概率生成预测
        predictions = []
        for i in range(num_predictions):
            # 使用加权随机选择
            numbers = list(range(1, 38))
            weights = [main_probs[n] for n in numbers]
            
            # 选择7个主号码
            main_nums = sorted(np.random.choice(
                numbers, size=7, replace=False, p=weights
            ))
            
            # 选择2个奖励号码 (不与主号码重复)
            available = [n for n in numbers if n not in main_nums]
            bonus_weights = [bonus_probs[n] for n in available]
            bonus_weights = np.array(bonus_weights) / sum(bonus_weights)
            
            bonus_nums = sorted(np.random.choice(
                available, size=2, replace=False, p=bonus_weights
            ))
            
            print(f"预测 #{i+1}:")
            print(f"  主号码: {' '.join(f'{n:2d}' for n in main_nums)}")
            print(f"  奖励号码: {' '.join(f'{n:2d}' for n in bonus_nums)}")
            
            predictions.append({
                'main_numbers': list(main_nums),
                'bonus_numbers': list(bonus_nums)
            })
        
        return predictions
    
    def visualize_analysis(self):
        """可视化分析结果"""
        print("\n=== 生成可视化图表 ===")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 主号码频率分布
        main_freq = Counter(self.main_numbers)
        numbers = list(range(1, 38))
        frequencies = [main_freq.get(n, 0) for n in numbers]
        
        axes[0, 0].bar(numbers, frequencies, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('主号码出现频率分布')
        axes[0, 0].set_xlabel('号码')
        axes[0, 0].set_ylabel('出现次数')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 奖励号码频率分布
        bonus_freq = Counter(self.bonus_numbers)
        bonus_frequencies = [bonus_freq.get(n, 0) for n in numbers]
        
        axes[0, 1].bar(numbers, bonus_frequencies, alpha=0.7, color='lightcoral')
        axes[0, 1].set_title('奖励号码出现频率分布')
        axes[0, 1].set_xlabel('号码')
        axes[0, 1].set_ylabel('出现次数')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 号码和分布
        number_sums = []
        for _, row in self.df.iterrows():
            numbers = [row[f'number_{i}'] for i in range(1, 8)]
            number_sums.append(sum(numbers))
        
        axes[1, 0].hist(number_sums, bins=30, alpha=0.7, color='lightgreen')
        axes[1, 0].set_title('号码和分布')
        axes[1, 0].set_xlabel('号码和')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].axvline(np.mean(number_sums), color='red', linestyle='--', 
                          label=f'平均值: {np.mean(number_sums):.1f}')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 奇偶分布
        odd_counts = []
        for _, row in self.df.iterrows():
            numbers = [row[f'number_{i}'] for i in range(1, 8)]
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            odd_counts.append(odd_count)
        
        odd_dist = Counter(odd_counts)
        axes[1, 1].bar(odd_dist.keys(), odd_dist.values(), alpha=0.7, color='gold')
        axes[1, 1].set_title('奇数个数分布')
        axes[1, 1].set_xlabel('奇数个数')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('loto7_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为 'loto7_analysis.png'")
        
    def run_complete_analysis(self):
        """运行完整分析"""
        print("=" * 60)
        print("Loto 7 蒙特卡洛预测分析")
        print("=" * 60)
        
        # 1. 频率分析
        self.analyze_frequency()
        
        # 2. 模式分析
        self.analyze_patterns()
        
        # 3. 蒙特卡洛模拟
        simulated_combinations = self.monte_carlo_simulation(100000)
        
        # 4. 选择最佳预测
        best_predictions = self.select_best_predictions(simulated_combinations, 5)
        
        # 5. 生成额外预测
        additional_predictions = self.generate_additional_predictions(5)
        
        # 6. 可视化
        self.visualize_analysis()
        
        # 7. 总结
        print("\n" + "=" * 60)
        print("预测总结")
        print("=" * 60)
        print("基于630期历史数据的蒙特卡洛分析完成")
        print("预测方法: 概率加权 + 蒙特卡洛模拟")
        print("建议: 以上预测仅供参考，彩票具有随机性")
        print("=" * 60)
        
        return best_predictions, additional_predictions

def main():
    # 创建预测器
    predictor = Loto7MonteCarloPredictor()
    
    # 运行完整分析
    best_predictions, additional_predictions = predictor.run_complete_analysis()
    
    return predictor, best_predictions, additional_predictions

if __name__ == "__main__":
    predictor, best_predictions, additional_predictions = main()
