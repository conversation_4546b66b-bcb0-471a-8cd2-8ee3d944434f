#!/usr/bin/env python3
"""
Loto 7 Number Scraper
Scrapes all historical Loto 7 numbers from Rakuten Takarakuji website
"""

import requests
from bs4 import BeautifulSoup
import csv
import time
import re
from datetime import datetime
from urllib.parse import urljoin
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Loto7Scraper:
    def __init__(self):
        self.base_url = "https://takarakuji.rakuten.co.jp"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.all_results = []
        
    def get_page(self, url):
        """Get a page with error handling and rate limiting"""
        try:
            time.sleep(1)  # Be respectful to the server
            response = self.session.get(url)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def parse_main_index(self):
        """Parse the main index page to get all URLs"""
        url = f"{self.base_url}/backnumber/loto7_past/"
        html = self.get_page(url)
        if not html:
            return [], []
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Get monthly URLs (recent data)
        monthly_urls = []
        monthly_links = soup.find_all('a', href=re.compile(r'/backnumber/loto7/\d{6}/'))
        for link in monthly_links:
            monthly_urls.append(urljoin(self.base_url, link['href']))
        
        # Get range URLs (older data)
        range_urls = []
        range_links = soup.find_all('a', href=re.compile(r'/backnumber/loto7_detail/\d{4}-\d{4}/'))
        for link in range_links:
            range_urls.append(urljoin(self.base_url, link['href']))
        
        logger.info(f"Found {len(monthly_urls)} monthly URLs and {len(range_urls)} range URLs")
        return monthly_urls, range_urls
    
    def parse_monthly_page(self, url):
        """Parse a monthly page (recent format with detailed info)"""
        html = self.get_page(url)
        if not html:
            return []
        
        soup = BeautifulSoup(html, 'html.parser')
        results = []
        
        # Find all draw results in the page
        # Look for patterns that indicate draw data
        text = soup.get_text()
        
        # Extract draw numbers and data using regex patterns
        draw_pattern = r'第(\d+)回.*?(\d{4}/\d{2}/\d{2}).*?本数字\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*ボーナス数字\s*\((\d+)\)\s*\((\d+)\)'
        matches = re.findall(draw_pattern, text, re.DOTALL)
        
        for match in matches:
            draw_num, date, n1, n2, n3, n4, n5, n6, n7, b1, b2 = match
            result = {
                'draw_number': int(draw_num),
                'date': date,
                'main_numbers': [int(n1), int(n2), int(n3), int(n4), int(n5), int(n6), int(n7)],
                'bonus_numbers': [int(b1), int(b2)]
            }
            results.append(result)
            
        logger.info(f"Extracted {len(results)} results from {url}")
        return results
    
    def parse_range_page(self, url):
        """Parse a range page (older format with table)"""
        html = self.get_page(url)
        if not html:
            return []
        
        soup = BeautifulSoup(html, 'html.parser')
        results = []
        
        # Find the table with lottery results
        # Look for table rows with draw data
        text = soup.get_text()
        
        # Extract data using regex for the table format
        # Pattern for: 第XXXX回 YYYY/MM/DD numbers bonus_numbers
        draw_pattern = r'第(\d+)回\s*(\d{4}/\d{2}/\d{2})\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)\s*(\d+)'
        matches = re.findall(draw_pattern, text)
        
        for match in matches:
            draw_num, date, n1, n2, n3, n4, n5, n6, n7, b1, b2 = match
            result = {
                'draw_number': int(draw_num),
                'date': date,
                'main_numbers': [int(n1), int(n2), int(n3), int(n4), int(n5), int(n6), int(n7)],
                'bonus_numbers': [int(b1), int(b2)]
            }
            results.append(result)
            
        logger.info(f"Extracted {len(results)} results from {url}")
        return results
    
    def scrape_all_data(self):
        """Scrape all Loto 7 data"""
        logger.info("Starting Loto 7 data scraping...")
        
        # Get all URLs
        monthly_urls, range_urls = self.parse_main_index()
        
        # Scrape monthly pages (recent data)
        logger.info("Scraping monthly pages...")
        for url in monthly_urls:
            results = self.parse_monthly_page(url)
            self.all_results.extend(results)
        
        # Scrape range pages (older data)
        logger.info("Scraping range pages...")
        for url in range_urls:
            results = self.parse_range_page(url)
            self.all_results.extend(results)
        
        # Sort results by draw number
        self.all_results.sort(key=lambda x: x['draw_number'])
        
        logger.info(f"Total results collected: {len(self.all_results)}")
        return self.all_results
    
    def save_to_csv(self, filename='loto7_all_results.csv'):
        """Save results to CSV file"""
        if not self.all_results:
            logger.warning("No results to save")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['draw_number', 'date', 'number_1', 'number_2', 'number_3', 'number_4', 
                         'number_5', 'number_6', 'number_7', 'bonus_1', 'bonus_2']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in self.all_results:
                row = {
                    'draw_number': result['draw_number'],
                    'date': result['date'],
                    'number_1': result['main_numbers'][0],
                    'number_2': result['main_numbers'][1],
                    'number_3': result['main_numbers'][2],
                    'number_4': result['main_numbers'][3],
                    'number_5': result['main_numbers'][4],
                    'number_6': result['main_numbers'][5],
                    'number_7': result['main_numbers'][6],
                    'bonus_1': result['bonus_numbers'][0],
                    'bonus_2': result['bonus_numbers'][1]
                }
                writer.writerow(row)
        
        logger.info(f"Results saved to {filename}")

def main():
    scraper = Loto7Scraper()
    scraper.scrape_all_data()
    scraper.save_to_csv()
    
    print(f"\nScraping completed!")
    print(f"Total draws collected: {len(scraper.all_results)}")
    if scraper.all_results:
        print(f"Date range: {scraper.all_results[0]['date']} to {scraper.all_results[-1]['date']}")
        print(f"Draw range: #{scraper.all_results[0]['draw_number']} to #{scraper.all_results[-1]['draw_number']}")

if __name__ == "__main__":
    main()
