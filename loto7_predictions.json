[{"id": 7, "main_numbers": [8, 9, 10, 23, 24, 27, 34], "bonus_numbers": [12, 19], "score": 32, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 9, "main_numbers": [1, 7, 10, 12, 20, 29, 34], "bonus_numbers": [24, 30], "score": 29, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 6, "main_numbers": [5, 10, 21, 23, 26, 30, 33], "bonus_numbers": [24, 25], "score": 28, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 3, "main_numbers": [1, 2, 4, 6, 9, 21, 30], "bonus_numbers": [13, 24], "score": 26, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 4, "main_numbers": [6, 9, 10, 17, 26, 27, 37], "bonus_numbers": [19, 24], "score": 26, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 5, "main_numbers": [4, 5, 10, 15, 23, 28, 33], "bonus_numbers": [13, 19], "score": 23, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 2, "main_numbers": [3, 4, 7, 13, 18, 24, 26], "bonus_numbers": [10, 30], "score": 22, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 8, "main_numbers": [3, 4, 7, 18, 25, 31, 33], "bonus_numbers": [12, 24], "score": 22, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 1, "main_numbers": [6, 10, 16, 22, 25, 29, 30], "bonus_numbers": [13, 19], "score": 20, "strategy": "热温冷号组合 + 趋势分析"}, {"id": 10, "main_numbers": [3, 5, 7, 10, 24, 26, 31], "bonus_numbers": [12, 21], "score": 16, "strategy": "热温冷号组合 + 趋势分析"}]