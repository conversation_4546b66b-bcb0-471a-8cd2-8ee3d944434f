#!/usr/bin/env python3
"""
Loto 7 高级预测报告生成器
基于蒙特卡洛分析生成详细的预测报告
"""

import pandas as pd
import numpy as np
from collections import Counter
import random
from datetime import datetime
import json

class Loto7AdvancedPredictor:
    def __init__(self, csv_file='loto7_all_results.csv'):
        self.df = pd.read_csv(csv_file)
        self.main_numbers = []
        self.bonus_numbers = []
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        for i in range(1, 8):
            self.main_numbers.extend(self.df[f'number_{i}'].tolist())
        self.bonus_numbers.extend(self.df['bonus_1'].tolist())
        self.bonus_numbers.extend(self.df['bonus_2'].tolist())
    
    def advanced_frequency_analysis(self):
        """高级频率分析"""
        main_freq = Counter(self.main_numbers)
        bonus_freq = Counter(self.bonus_numbers)
        
        # 计算热号、温号、冷号
        main_sorted = main_freq.most_common()
        total_numbers = len(main_sorted)
        
        hot_numbers = [num for num, freq in main_sorted[:12]]  # 前1/3为热号
        warm_numbers = [num for num, freq in main_sorted[12:25]]  # 中间1/3为温号
        cold_numbers = [num for num, freq in main_sorted[25:]]  # 后1/3为冷号
        
        return {
            'hot_numbers': hot_numbers,
            'warm_numbers': warm_numbers,
            'cold_numbers': cold_numbers,
            'main_freq': main_freq,
            'bonus_freq': bonus_freq
        }
    
    def analyze_recent_trends(self, recent_draws=50):
        """分析最近趋势"""
        recent_data = self.df.tail(recent_draws)
        recent_main = []
        recent_bonus = []
        
        for _, row in recent_data.iterrows():
            for i in range(1, 8):
                recent_main.append(row[f'number_{i}'])
            recent_bonus.extend([row['bonus_1'], row['bonus_2']])
        
        recent_main_freq = Counter(recent_main)
        recent_bonus_freq = Counter(recent_bonus)
        
        return {
            'recent_main_freq': recent_main_freq,
            'recent_bonus_freq': recent_bonus_freq,
            'trending_up': recent_main_freq.most_common(10),
            'recent_draws': recent_draws
        }
    
    def calculate_number_gaps(self):
        """计算号码间隔分析"""
        number_last_appearance = {}
        number_gaps = {i: [] for i in range(1, 38)}
        
        for idx, row in self.df.iterrows():
            current_numbers = [row[f'number_{i}'] for i in range(1, 8)]
            
            # 更新间隔
            for num in range(1, 38):
                if num in current_numbers:
                    if num in number_last_appearance:
                        gap = idx - number_last_appearance[num]
                        number_gaps[num].append(gap)
                    number_last_appearance[num] = idx
        
        # 计算平均间隔
        avg_gaps = {}
        for num in range(1, 38):
            if number_gaps[num]:
                avg_gaps[num] = np.mean(number_gaps[num])
            else:
                avg_gaps[num] = len(self.df)  # 如果从未出现，设为最大间隔
        
        return avg_gaps, number_last_appearance
    
    def generate_smart_predictions(self, num_predictions=10):
        """生成智能预测"""
        freq_analysis = self.advanced_frequency_analysis()
        trend_analysis = self.analyze_recent_trends()
        avg_gaps, last_appearance = self.calculate_number_gaps()
        
        predictions = []
        
        for i in range(num_predictions):
            # 策略组合：热号+温号+冷号+趋势号
            main_numbers = []
            
            # 选择2-3个热号
            hot_count = random.randint(2, 3)
            selected_hot = random.sample(freq_analysis['hot_numbers'], hot_count)
            main_numbers.extend(selected_hot)
            
            # 选择2-3个温号
            warm_count = random.randint(2, 3)
            available_warm = [n for n in freq_analysis['warm_numbers'] if n not in main_numbers]
            if len(available_warm) >= warm_count:
                selected_warm = random.sample(available_warm, warm_count)
                main_numbers.extend(selected_warm)
            
            # 选择1-2个冷号
            cold_count = 7 - len(main_numbers)
            if cold_count > 0:
                available_cold = [n for n in freq_analysis['cold_numbers'] if n not in main_numbers]
                if len(available_cold) >= cold_count:
                    selected_cold = random.sample(available_cold, cold_count)
                    main_numbers.extend(selected_cold)
            
            # 如果数量不够，从剩余号码中随机选择
            while len(main_numbers) < 7:
                available = [n for n in range(1, 38) if n not in main_numbers]
                if available:
                    main_numbers.append(random.choice(available))
                else:
                    break
            
            main_numbers = sorted(main_numbers[:7])
            
            # 选择奖励号码
            bonus_numbers = []
            trending_bonus = [num for num, freq in trend_analysis['recent_bonus_freq'].most_common(10)]
            available_bonus = [n for n in trending_bonus if n not in main_numbers]
            
            if len(available_bonus) >= 2:
                bonus_numbers = random.sample(available_bonus, 2)
            else:
                # 从所有可用号码中选择
                all_available = [n for n in range(1, 38) if n not in main_numbers]
                bonus_numbers = random.sample(all_available, 2)
            
            bonus_numbers = sorted(bonus_numbers)
            
            # 计算预测质量分数
            score = self.calculate_prediction_score(main_numbers, bonus_numbers, freq_analysis, trend_analysis)
            
            predictions.append({
                'id': i + 1,
                'main_numbers': main_numbers,
                'bonus_numbers': bonus_numbers,
                'score': score,
                'strategy': '热温冷号组合 + 趋势分析'
            })
        
        # 按分数排序
        predictions.sort(key=lambda x: x['score'], reverse=True)
        return predictions
    
    def calculate_prediction_score(self, main_nums, bonus_nums, freq_analysis, trend_analysis):
        """计算预测质量分数"""
        score = 0
        
        # 频率分数
        for num in main_nums:
            if num in freq_analysis['hot_numbers']:
                score += 3
            elif num in freq_analysis['warm_numbers']:
                score += 2
            else:
                score += 1
        
        # 趋势分数
        trending_nums = [num for num, freq in trend_analysis['trending_up']]
        for num in main_nums:
            if num in trending_nums:
                score += 2
        
        # 奖励号码分数
        for num in bonus_nums:
            if num in [n for n, f in trend_analysis['recent_bonus_freq'].most_common(5)]:
                score += 2
        
        # 号码和分数 (接近历史平均值)
        number_sum = sum(main_nums)
        if 120 <= number_sum <= 150:  # 接近历史平均133.8
            score += 5
        
        # 奇偶平衡分数
        odd_count = sum(1 for n in main_nums if n % 2 == 1)
        if 3 <= odd_count <= 4:  # 接近历史平均3.6
            score += 3
        
        return score
    
    def generate_final_report(self):
        """生成最终预测报告"""
        print("=" * 80)
        print("🎯 Loto 7 高级蒙特卡洛预测报告")
        print("=" * 80)
        
        # 基础统计
        freq_analysis = self.advanced_frequency_analysis()
        trend_analysis = self.analyze_recent_trends()
        
        print(f"\n📊 数据概览:")
        print(f"   总期数: {len(self.df)} 期")
        print(f"   分析范围: {self.df.iloc[0]['date']} - {self.df.iloc[-1]['date']}")
        print(f"   最近趋势分析: 最近 {trend_analysis['recent_draws']} 期")
        
        print(f"\n🔥 热号 (出现频率最高):")
        print(f"   {', '.join(map(str, freq_analysis['hot_numbers']))}")
        
        print(f"\n🌡️ 温号 (出现频率中等):")
        print(f"   {', '.join(map(str, freq_analysis['warm_numbers']))}")
        
        print(f"\n❄️ 冷号 (出现频率较低):")
        print(f"   {', '.join(map(str, freq_analysis['cold_numbers']))}")
        
        print(f"\n📈 最近趋势 (最近{trend_analysis['recent_draws']}期热门号码):")
        trending = [str(num) for num, freq in trend_analysis['trending_up']]
        print(f"   {', '.join(trending)}")
        
        # 生成智能预测
        predictions = self.generate_smart_predictions(10)
        
        print(f"\n🎯 智能预测 (按质量分数排序):")
        print("=" * 80)
        
        for pred in predictions:
            print(f"\n预测 #{pred['id']} (质量分数: {pred['score']})")
            print(f"   主号码: {' '.join(f'{n:2d}' for n in pred['main_numbers'])}")
            print(f"   奖励号码: {' '.join(f'{n:2d}' for n in pred['bonus_numbers'])}")
            print(f"   策略: {pred['strategy']}")
            
            # 分析这组号码
            main_sum = sum(pred['main_numbers'])
            odd_count = sum(1 for n in pred['main_numbers'] if n % 2 == 1)
            hot_count = sum(1 for n in pred['main_numbers'] if n in freq_analysis['hot_numbers'])
            
            print(f"   号码和: {main_sum} | 奇数个数: {odd_count} | 热号个数: {hot_count}")
        
        # 推荐组合
        print(f"\n⭐ 特别推荐 (前3组):")
        print("=" * 80)
        
        for i, pred in enumerate(predictions[:3], 1):
            print(f"\n🏆 推荐 #{i}:")
            print(f"   主号码: {' - '.join(f'{n:02d}' for n in pred['main_numbers'])}")
            print(f"   奖励号码: {' - '.join(f'{n:02d}' for n in pred['bonus_numbers'])}")
        
        print(f"\n" + "=" * 80)
        print("📝 预测说明:")
        print("   • 基于630期历史数据的统计分析")
        print("   • 采用热温冷号码组合策略")
        print("   • 结合最近趋势和蒙特卡洛模拟")
        print("   • 质量分数综合考虑频率、趋势、数字特征")
        print("   • 仅供参考，彩票具有随机性")
        print("=" * 80)
        
        return predictions

def main():
    predictor = Loto7AdvancedPredictor()
    predictions = predictor.generate_final_report()
    
    # 保存预测结果
    with open('loto7_predictions.json', 'w', encoding='utf-8') as f:
        json.dump(predictions, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 预测结果已保存到 'loto7_predictions.json'")
    
    return predictions

if __name__ == "__main__":
    predictions = main()
